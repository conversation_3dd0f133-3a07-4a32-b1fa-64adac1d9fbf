"""
深度图像处理工具
"""
import numpy as np
import math
from typing import List, Tuple, Dict, Any

def process_depth_image(depth_img: np.ndarray, max_depth: float = 100.0, sky_depth: float = 100.0) -> np.ndarray:
    """
    处理深度图像

    Args:
        depth_img: 深度图像数组
        max_depth: 最大深度值，超过此值的被视为天空
        sky_depth: 天空的深度值

    Returns:
        处理后的深度图像
    """
    # 创建深度图像的副本
    processed_depth = depth_img.copy()

    # 将无效值（0或NaN）设为最大深度
    processed_depth[np.isnan(processed_depth) | (processed_depth <= 0)] = max_depth

    # 将超过最大深度的值（天空）设为指定的天空深度值
    processed_depth[processed_depth > max_depth] = sky_depth

    return processed_depth

def depth_to_point_cloud(depth_img: np.ndarray, fov_h: float = 90.0, depth_scale: float = 1.0) -> np.ndarray:
    """
    将深度图像转换为点云

    Args:
        depth_img: 深度图像数组
        fov_h: 水平视场角（度）
        depth_scale: 深度缩放因子

    Returns:
        点云数组 (N, 3)，坐标系为：x向前，y向右，z向下
    """
    height, width = depth_img.shape

    # 计算相机内参
    f = width / (2 * math.tan(math.radians(fov_h) / 2))  # 焦距
    cx = width / 2  # 光心x
    cy = height / 2  # 光心y

    # 创建网格
    xs, ys = np.meshgrid(np.arange(width), np.arange(height))

    # 计算归一化坐标
    x_norm = (xs - cx) / f
    y_norm = (ys - cy) / f

    # 应用深度缩放
    scaled_depth = depth_img * depth_scale

    # 计算点云坐标 (相机坐标系)
    # AirSim相机坐标系：x向前，y向右，z向下
    points_x = scaled_depth  # 深度值对应x轴（向前）
    points_y = scaled_depth * x_norm  # 右方向对应y轴
    points_z = scaled_depth * y_norm  # 下方向对应z轴

    # 创建点云数组
    points = np.stack((points_x.flatten(), points_y.flatten(), points_z.flatten()), axis=1)

    # 过滤无效点（深度为0或无穷大）
    valid_indices = np.isfinite(points[:, 0]) & (points[:, 0] > 0)
    points = points[valid_indices]

    return points

def get_front_points(points: np.ndarray, angle_range: float = 180.0) -> np.ndarray:
    """
    获取前方指定角度范围内的点云

    Args:
        points: 点云数组 (N, 3)
        angle_range: 角度范围（度）

    Returns:
        前方点云
    """
    # 计算每个点的水平角度（在xOy平面上）
    # 注意：AirSim相机坐标系中，x向前，y向右
    angles = np.arctan2(points[:, 1], points[:, 0])  # 弧度，[-pi, pi]

    # 将角度转换为度
    angles_deg = np.degrees(angles)  # [-180, 180]

    # 计算角度范围
    half_range = angle_range / 2

    # 选择前方指定角度范围内的点
    front_indices = (angles_deg >= -half_range) & (angles_deg <= half_range)
    front_points = points[front_indices]

    return front_points

def points_to_distance_array(points: np.ndarray, num_bins: int = 72, angle_range: float = 90.0) -> np.ndarray:
    """
    将点云转换为距离数组（类似于2D激光雷达数据）

    Args:
        points: 点云数组 (N, 3)
        num_bins: 角度分区数量
        angle_range: 角度范围（度），应该与相机的水平视场角匹配

    Returns:
        距离数组
    """
    if len(points) == 0:
        return np.full(num_bins, 100.0)  # 如果没有点，返回最大距离

    # 计算每个点的水平角度和距离
    angles = np.arctan2(points[:, 1], points[:, 0])  # 弧度，[-pi, pi]
    distances = np.sqrt(points[:, 0]**2 + points[:, 1]**2)  # 水平距离

    # 将角度转换为度
    angles_deg = np.degrees(angles)  # [-180, 180]

    # 计算角度范围
    half_range = angle_range / 2

    # 创建角度分区
    angle_bins = np.linspace(-half_range, half_range, num_bins + 1)

    # 初始化距离数组
    distance_array = np.full(num_bins, 100.0)  # 默认最大距离

    # 将点分配到角度分区中，并找到每个分区中的最小距离
    for i in range(len(distances)):
        if angles_deg[i] >= -half_range and angles_deg[i] <= half_range:
            bin_idx = np.digitize(angles_deg[i], angle_bins) - 1
            if bin_idx < num_bins:  # 确保索引有效
                distance_array[bin_idx] = min(distance_array[bin_idx], distances[i])

    return distance_array

def detect_collision_directions(points: np.ndarray, drone_radius: float = 0.3, min_distance: float = 1.5,
                               num_directions: int = 36, vertical_fov: float = 45.0) -> np.ndarray:
    """
    检测可能发生碰撞的方向

    Args:
        points: 点云数组 (N, 3)
        drone_radius: 无人机半径（米）
        min_distance: 最小安全距离（米）
        num_directions: 方向数量
        vertical_fov: 垂直视场角（度）

    Returns:
        碰撞风险数组，值越大表示碰撞风险越高
    """
    if len(points) == 0:
        return np.zeros(num_directions)

    # 计算方向角度（水平方向）
    direction_angles = np.linspace(-180, 180, num_directions, endpoint=False)  # 度

    # 初始化碰撞风险数组
    collision_risks = np.zeros(num_directions)

    # 计算每个点的水平角度和距离
    point_angles = np.arctan2(points[:, 1], points[:, 0])  # 弧度，[-pi, pi]
    point_angles_deg = np.degrees(point_angles)  # [-180, 180]

    # 计算水平距离
    horizontal_distances = np.sqrt(points[:, 0]**2 + points[:, 1]**2)

    # 过滤掉太远的点（超过安全距离的3倍）
    # 这样可以减少对远处物体的过度反应
    valid_distance_indices = horizontal_distances < min_distance * 3.0
    if not np.any(valid_distance_indices):
        return np.zeros(num_directions)

    points = points[valid_distance_indices]
    point_angles = point_angles[valid_distance_indices]
    point_angles_deg = point_angles_deg[valid_distance_indices]
    horizontal_distances = horizontal_distances[valid_distance_indices]

    # 计算垂直角度
    vertical_angles = np.arctan2(points[:, 2], horizontal_distances)  # 弧度，[-pi/2, pi/2]
    vertical_angles_deg = np.degrees(vertical_angles)  # [-90, 90]

    # 垂直视场角范围
    half_vfov = vertical_fov / 2

    # 对每个方向评估碰撞风险
    for i, direction in enumerate(direction_angles):
        # 计算方向的弧度表示
        direction_rad = np.radians(direction)

        # 计算每个点相对于该方向的角度差
        angle_diffs = np.abs(np.arctan2(np.sin(point_angles - direction_rad), np.cos(point_angles - direction_rad)))
        angle_diffs_deg = np.degrees(angle_diffs)

        # 选择在该方向前方的点（角度差小于90度）
        front_indices = angle_diffs_deg <= 90

        if not np.any(front_indices):
            continue

        front_points = points[front_indices]
        front_distances = horizontal_distances[front_indices]
        front_angle_diffs = angle_diffs_deg[front_indices]
        front_vertical_angles = vertical_angles_deg[front_indices]

        # 选择在垂直视场角范围内的点
        vertical_indices = (front_vertical_angles >= -half_vfov) & (front_vertical_angles <= half_vfov)

        if not np.any(vertical_indices):
            continue

        valid_distances = front_distances[vertical_indices]
        valid_angle_diffs = front_angle_diffs[vertical_indices]

        # 计算每个点的碰撞风险
        # 距离越近，角度差越小，风险越高
        # 使用更敏感的距离衰减函数
        distance_factors = 1.0 / (0.5 + valid_distances / min_distance)

        # 使用更敏感的角度衰减函数
        angle_factors = 1.0 / (0.5 + valid_angle_diffs / 30.0)  # 30度作为参考角度

        # 增加前方点的权重
        front_weights = np.ones_like(valid_angle_diffs)
        front_indices = valid_angle_diffs < 15.0  # 前方15度范围内
        if np.any(front_indices):
            front_weights[front_indices] = 1.5  # 增加前方点的权重

        point_risks = distance_factors * angle_factors * front_weights

        # 取最大风险作为该方向的风险
        if len(point_risks) > 0:
            collision_risks[i] = np.max(point_risks)

    # 平滑碰撞风险数组，避免突变
    smoothed_risks = np.zeros_like(collision_risks)
    for i in range(len(collision_risks)):
        # 使用周围方向的风险进行平均
        indices = [(i-1) % num_directions, i, (i+1) % num_directions]
        weights = [0.25, 0.5, 0.25]  # 中心权重更高
        smoothed_risks[i] = np.sum(collision_risks[indices] * weights)

    return smoothed_risks

def find_safe_direction(collision_risks: np.ndarray, current_direction: float = 0.0,
                        max_turn_angle: float = 60.0) -> float:
    """
    找到最安全的方向

    Args:
        collision_risks: 碰撞风险数组
        current_direction: 当前方向（度）
        max_turn_angle: 最大转向角（度）

    Returns:
        最安全方向（度，相对于当前方向）
    """
    num_directions = len(collision_risks)

    # 计算方向角度
    direction_angles = np.linspace(-180, 180, num_directions, endpoint=False)  # 度

    # 计算每个方向的安全度（1 - 风险）
    safety_scores = 1.0 - collision_risks

    # 增加前方方向的偏好（优先选择正前方）
    for i, angle in enumerate(direction_angles):
        # 角度偏离惩罚
        angle_penalty = abs(angle) / 180.0
        # 减少安全度，但保持相对顺序
        safety_scores[i] *= (1.0 - 0.3 * angle_penalty)

    # 考虑转向角度限制
    for i, angle in enumerate(direction_angles):
        # 计算相对于当前方向的转向角
        turn_angle = (angle - current_direction + 180) % 360 - 180

        # 如果转向角超过最大限制，降低安全度
        if abs(turn_angle) > max_turn_angle:
            safety_scores[i] = 0.0
        else:
            # 对于在最大转向角范围内的方向，根据转向角大小进行轻微惩罚
            # 这样可以在安全的情况下优先选择转向角较小的方向
            turn_penalty = abs(turn_angle) / max_turn_angle
            safety_scores[i] *= (1.0 - 0.2 * turn_penalty)

    # 找到最安全的方向
    safest_idx = np.argmax(safety_scores)
    safest_angle = direction_angles[safest_idx]

    # 计算相对于当前方向的转向角
    turn_angle = (safest_angle - current_direction + 180) % 360 - 180

    # 如果风险普遍较低，减小转向角度，但保持一定的敏感度
    if np.max(collision_risks) < 0.1:  # 最大风险非常低
        turn_angle *= 0.5  # 减小转向角度
    elif np.max(collision_risks) < 0.3:  # 最大风险较低
        turn_angle *= 0.7  # 稍微减小转向角度

    return turn_angle


def create_3d_grid_from_points(points: np.ndarray, grid_h_bins: int = 16, grid_v_bins: int = 8,
                              grid_d_bins: int = 10, max_range: float = 15.0) -> np.ndarray:
    """
    将点云转换为3D网格，每个网格单元包含该区域内的最小距离

    Args:
        points: 点云数组 (N, 3)，坐标系为：x向前，y向右，z向下
        grid_h_bins: 水平方向网格数量
        grid_v_bins: 垂直方向网格数量
        grid_d_bins: 深度方向网格数量
        max_range: 最大检测范围（米）

    Returns:
        3D网格数组 (grid_h_bins, grid_v_bins, grid_d_bins)，值为该网格内的最小距离
    """
    if len(points) == 0:
        return np.full((grid_h_bins, grid_v_bins, grid_d_bins), max_range)

    # 过滤掉超出检测范围的点
    distances = np.linalg.norm(points, axis=1)
    valid_indices = distances <= max_range
    valid_points = points[valid_indices]

    if len(valid_points) == 0:
        return np.full((grid_h_bins, grid_v_bins, grid_d_bins), max_range)

    # 计算网格边界
    # 水平方向：基于y坐标（左右）
    y_min, y_max = -max_range * 0.7, max_range * 0.7  # 左右范围
    # 垂直方向：基于z坐标（上下）
    z_min, z_max = -max_range * 0.5, max_range * 0.5  # 上下范围
    # 深度方向：基于x坐标（前后）
    x_min, x_max = 0.5, max_range  # 前方范围，避免包含无人机自身

    # 初始化3D网格
    grid_3d = np.full((grid_h_bins, grid_v_bins, grid_d_bins), max_range)

    # 计算每个点的网格索引
    h_indices = np.floor((valid_points[:, 1] - y_min) / (y_max - y_min) * grid_h_bins).astype(int)
    v_indices = np.floor((valid_points[:, 2] - z_min) / (z_max - z_min) * grid_v_bins).astype(int)
    d_indices = np.floor((valid_points[:, 0] - x_min) / (x_max - x_min) * grid_d_bins).astype(int)

    # 确保索引在有效范围内
    h_indices = np.clip(h_indices, 0, grid_h_bins - 1)
    v_indices = np.clip(v_indices, 0, grid_v_bins - 1)
    d_indices = np.clip(d_indices, 0, grid_d_bins - 1)

    # 计算每个点到无人机的距离
    point_distances = np.linalg.norm(valid_points, axis=1)

    # 将点分配到网格中，保留每个网格的最小距离
    for i in range(len(valid_points)):
        h_idx, v_idx, d_idx = h_indices[i], v_indices[i], d_indices[i]
        grid_3d[h_idx, v_idx, d_idx] = min(grid_3d[h_idx, v_idx, d_idx], point_distances[i])

    return grid_3d


def evaluate_3d_velocity_safety(grid_3d: np.ndarray, velocity_candidates: np.ndarray,
                               drone_radius: float = 0.3, min_distance: float = 6.0,
                               max_range: float = 15.0, time_horizon: float = 3.0) -> np.ndarray:
    """
    评估3D速度候选的安全性

    Args:
        grid_3d: 3D网格数组 (grid_h_bins, grid_v_bins, grid_d_bins)
        velocity_candidates: 速度候选数组 (N, 3)，每行为一个速度向量[Vx, Vy, Vz]
        drone_radius: 无人机半径（米）
        min_distance: 最小安全距离（米）
        max_range: 最大检测范围（米）
        time_horizon: 预测时间范围（秒）

    Returns:
        安全性评分数组 (N,)，值越高越安全
    """
    grid_h_bins, grid_v_bins, grid_d_bins = grid_3d.shape
    safety_scores = np.zeros(len(velocity_candidates))

    # 网格边界
    y_min, y_max = -max_range * 0.7, max_range * 0.7
    z_min, z_max = -max_range * 0.5, max_range * 0.5
    x_min, x_max = 0.5, max_range

    # 网格单元大小
    h_step = (y_max - y_min) / grid_h_bins
    v_step = (z_max - z_min) / grid_v_bins
    d_step = (x_max - x_min) / grid_d_bins

    for i, velocity in enumerate(velocity_candidates):
        vx, vy, vz = velocity

        # 预测未来轨迹上的位置
        safety_score = 1.0
        collision_risk = 0.0

        # 在时间范围内采样多个时间点
        time_steps = np.linspace(0.1, time_horizon, 10)

        for t in time_steps:
            # 预测位置
            future_x = vx * t
            future_y = vy * t
            future_z = vz * t

            # 检查是否超出检测范围
            if future_x > max_range or abs(future_y) > max_range * 0.7 or abs(future_z) > max_range * 0.5:
                break

            # 计算网格索引
            h_idx = int((future_y - y_min) / h_step)
            v_idx = int((future_z - z_min) / v_step)
            d_idx = int((future_x - x_min) / d_step)

            # 确保索引在有效范围内
            if (0 <= h_idx < grid_h_bins and 0 <= v_idx < grid_v_bins and 0 <= d_idx < grid_d_bins):
                # 检查周围的网格单元（考虑无人机半径）
                radius_h = max(1, int(drone_radius / h_step))
                radius_v = max(1, int(drone_radius / v_step))
                radius_d = max(1, int(drone_radius / d_step))

                min_distance_in_area = max_range

                for dh in range(-radius_h, radius_h + 1):
                    for dv in range(-radius_v, radius_v + 1):
                        for dd in range(-radius_d, radius_d + 1):
                            nh_idx = h_idx + dh
                            nv_idx = v_idx + dv
                            nd_idx = d_idx + dd

                            if (0 <= nh_idx < grid_h_bins and 0 <= nv_idx < grid_v_bins and 0 <= nd_idx < grid_d_bins):
                                min_distance_in_area = min(min_distance_in_area, grid_3d[nh_idx, nv_idx, nd_idx])

                # 计算碰撞风险
                if min_distance_in_area < min_distance:
                    # 距离越近，风险越高
                    risk = (min_distance - min_distance_in_area) / min_distance
                    # 时间越近，风险权重越高
                    time_weight = (time_horizon - t) / time_horizon
                    collision_risk += risk * time_weight

        # 计算最终安全评分
        safety_score = max(0.0, 1.0 - collision_risk)

        # 添加速度偏好：优先前向运动，惩罚过大的侧向和垂直速度
        speed_penalty = 0.0
        speed_penalty += 0.1 * abs(vy) / max(abs(vx), 0.1)  # 侧向速度惩罚
        speed_penalty += 0.2 * abs(vz) / max(abs(vx), 0.1)  # 垂直速度惩罚

        # 惩罚后退
        if vx < 0:
            speed_penalty += 0.5

        safety_score *= max(0.1, 1.0 - speed_penalty)

        safety_scores[i] = safety_score

    return safety_scores


def generate_3d_velocity_candidates(base_speed: float = 1.0, num_candidates: int = 100) -> np.ndarray:
    """
    生成3D速度候选向量

    Args:
        base_speed: 基础速度大小（米/秒）
        num_candidates: 候选数量

    Returns:
        速度候选数组 (num_candidates, 3)
    """
    candidates = []

    # 生成不同方向的速度候选
    # 1. 主要前向运动 + 小幅侧向/垂直调整
    for i in range(num_candidates // 2):
        # 前向速度保持在80%-100%
        vx = base_speed * (0.8 + 0.2 * np.random.random())
        # 侧向速度在-30%到30%之间
        vy = base_speed * (np.random.random() - 0.5) * 0.6
        # 垂直速度在-20%到20%之间
        vz = base_speed * (np.random.random() - 0.5) * 0.4
        candidates.append([vx, vy, vz])

    # 2. 更大的机动性候选（用于紧急避障）
    for i in range(num_candidates // 4):
        # 前向速度可以降到50%
        vx = base_speed * (0.5 + 0.5 * np.random.random())
        # 侧向速度可以更大
        vy = base_speed * (np.random.random() - 0.5) * 1.0
        # 垂直速度可以更大
        vz = base_speed * (np.random.random() - 0.5) * 0.6
        candidates.append([vx, vy, vz])

    # 3. 特殊机动候选（悬停、上升、下降等）
    remaining = num_candidates - len(candidates)
    for i in range(remaining):
        if i % 4 == 0:
            # 悬停
            candidates.append([0.1, 0.0, 0.0])
        elif i % 4 == 1:
            # 上升
            candidates.append([base_speed * 0.7, 0.0, -base_speed * 0.3])
        elif i % 4 == 2:
            # 下降
            candidates.append([base_speed * 0.7, 0.0, base_speed * 0.3])
        else:
            # 随机候选
            vx = base_speed * np.random.random()
            vy = base_speed * (np.random.random() - 0.5) * 0.8
            vz = base_speed * (np.random.random() - 0.5) * 0.5
            candidates.append([vx, vy, vz])

    return np.array(candidates)
