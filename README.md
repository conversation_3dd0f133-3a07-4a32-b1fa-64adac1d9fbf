# 🚁 深度相机无人机真正3D避障系统

本项目实现了基于深度相机的无人机真正3D避障系统，使用3D网格空间划分和模仿学习方法。

## 🎯 项目特色

- **真正的3D空间避障**: 使用16x8x10网格划分3D空间，不再局限于水平面
- **三维速度控制**: 输出(Vx, Vy, Vz)速度向量，支持上升、下降、转向等3D机动
- **3D距离感知**: 基于真实的3D欧几里得距离进行风险评估
- **智能高度管理**: 高度限制机制确保飞行安全
- **实时3D轨迹预测**: 预测2秒内的3D飞行轨迹进行安全评估
- **模仿学习**: 教师模型(规则算法) + 学生模型(神经网络)
- **AirSim集成**: 在真实仿真环境中验证3D避障效果

## 项目结构

```
deep_il_3d/
├── config.py                    # 🔧 系统配置 (3D网格、高度限制等)
├── environment/                 # 🌍 环境控制模块
│   ├── drone_env.py            # 无人机环境 (3D速度控制、高度获取)
│   └── depth_utils.py          # 深度处理 (3D网格生成、3D距离计算)
├── models/                      # 🤖 AI模型模块
│   ├── teacher_model.py        # 教师模型 (3D规则避障、高度限制)
│   └── student_model.py        # 学生模型 (深度图像+高度→3D速度)
├── training/                    # 📚 训练模块
│   ├── data_collector.py       # 3D数据收集 (深度图像+高度+3D速度)
│   └── trainer.py              # 3D模型训练器
├── visualization/               # 📊 可视化工具
│   └── visualizer.py           # 数据可视化
├── collect_data.py             # 🗂️ 数据收集主脚本
├── train_model.py              # 🎓 模型训练主脚本
├── run_teacher.py              # 🧠 教师模型演示 (AirSim 3D避障)
├── run_student.py              # 🎯 学生模型运行
├── test_3d_avoidance.py        # 🧪 3D避障系统完整测试
├── demo_3d_avoidance.py        # 🎮 3D避障演示和可视化
├── test_run_teacher.py         # ✅ run_teacher功能测试
├── FINAL_3D_SUMMARY.md         # 📋 完整3D升级总结
├── test_depth_image.py         # 深度图像测试脚本
└── settings_rgb_deep.json      # AirSim配置文件
```

## 安装依赖

```bash
pip install airsim numpy torch matplotlib
```

## 🚀 使用方法

### 1. 🧪 测试3D避障系统

```bash
# 完整的3D避障系统测试
python test_3d_avoidance.py

# run_teacher功能测试 (无需AirSim)
python test_run_teacher.py
```

### 2. 🎮 3D避障演示

```bash
# 3D避障演示和可视化
python demo_3d_avoidance.py
```

### 3. 🧠 在AirSim中体验真正的3D避障

```bash
# 确保AirSim正在运行，然后运行教师模型
python run_teacher.py --max_steps 50 --visualize
```

### 4. 🗂️ 收集3D训练数据

```bash
# 收集包含深度图像+高度+3D速度向量的数据
python collect_data.py --episodes 100 --steps 50
```

### 5. 🎓 训练3D学生模型

```bash
# 训练支持3D输入输出的神经网络
python train_model.py --data_path data/depth_data_YYYYMMDD_HHMMSS_final.npz --epochs 100
```

### 6. 🎯 运行3D学生模型

```bash
# 运行训练好的3D学生模型
python run_student.py --model_path saved_models/student_model_latest.pth --max_steps 50 --visualize
```

## ⚙️ 配置参数

可以在`config.py`文件中修改以下参数：

### 🚁 无人机配置
- `TAKEOFF_HEIGHT`: 起飞高度
- `FORWARD_SPEED`: 前进速度 (总速度大小保持不变)
- `DRONE_RADIUS`: 无人机半径

### 📷 相机配置
- `CAMERA_NAME`: 相机名称
- `CAMERA_FOV_H/V`: 水平/垂直视场角
- `DEPTH_WIDTH/HEIGHT`: 深度图像分辨率

### 🎯 3D网格配置 (核心新功能)
- `GRID_HORIZONTAL_BINS`: 水平方向网格数量 (16)
- `GRID_VERTICAL_BINS`: 垂直方向网格数量 (8)
- `GRID_DEPTH_BINS`: 深度方向网格数量 (10)
- `MAX_DETECTION_RANGE`: 最大检测范围 (15米)

### 🛡️ 高度限制配置
- `HEIGHT_WARNING_THRESHOLD`: 高度警戒阈值 (5米)
- `HEIGHT_RESTRICTION_RATIO`: 可用区域比例 (0.4 = 下方40%可用)

### 🤖 学生模型配置
- `INPUT_CHANNELS`: 深度图像通道数
- `HEIGHT_INPUT_SIZE`: 高度输入维度
- `OUTPUT_SIZE`: 输出维度 (3 = Vx,Vy,Vz)
- `CONV_FILTERS/FC_SIZES`: 网络架构参数

## 🔬 核心技术实现

### 🌐 3D网格空间划分 (核心创新)

**替代传统的水平角度区间方法**

```python
# 创建16x8x10的3D网格 (总共1,280个空间单元)
grid_3d = create_3d_grid_from_points(
    points,                    # 3D点云数据
    GRID_HORIZONTAL_BINS=16,   # 水平方向16个网格
    GRID_VERTICAL_BINS=8,      # 垂直方向8个网格
    GRID_DEPTH_BINS=10,        # 深度方向10个网格
    MAX_DETECTION_RANGE=15.0   # 15米检测范围
)
```

### 📏 真正的3D距离计算

**不再只考虑水平距离**

```python
# 计算真正的3D欧几里得距离
point_distances = np.linalg.norm(valid_points, axis=1)
```

### 🎯 3D速度候选生成

**支持真正的3D机动**

```python
# 生成50个3D速度候选，包含：
# - 前向运动 + 侧向调整
# - 大机动性候选 (紧急避障)
# - 特殊机动 (悬停、上升、下降)
velocity_candidates = generate_3d_velocity_candidates(
    base_speed=FORWARD_SPEED,
    num_candidates=50
)
```

### 🛡️ 智能高度限制

**下方40%区域可用策略**

```python
if current_height > HEIGHT_WARNING_THRESHOLD:
    # 限制上部60%区域，保留下部40%区域
    restricted_height = int(DEPTH_HEIGHT * (1 - HEIGHT_RESTRICTION_RATIO))
    modified_depth[:restricted_height, :] = 0.1  # 设为高风险
```

### 🧠 教师模型 (3D规则避障)

1. **3D网格生成**: 将点云划分为1,280个3D网格单元
2. **3D轨迹预测**: 预测2秒内的3D飞行轨迹
3. **3D安全评估**: 评估每个3D速度候选的安全性
4. **高度限制**: 超高时自动限制上部区域
5. **紧急策略**: 危险时触发垂直机动

### 🤖 学生模型 (3D神经网络)

```
输入: 深度图像[72x128] + 当前高度[1]
     ↓
CNN特征提取: 深度图像 → 卷积特征
高度处理: 高度值 → 32维特征
     ↓
特征融合: [卷积特征 + 高度特征]
     ↓
全连接层: → [256] → [128] → [64]
     ↓
输出: [Vx, Vy, Vz] 三维速度向量
```

### 🗂️ 3D数据格式

**新的训练数据格式**

- **深度图像**: [N, 72, 128]
- **高度数据**: [N, 1]
- **动作数据**: [N, 3] (Vx, Vy, Vz)

### 🎮 3D控制系统

- **ForwardOnly模式**: 无人机自动朝向飞行方向
- **3D速度控制**: 真正的三维机动能力
- **总速度保持**: 速度大小始终为FORWARD_SPEED
- **机体坐标系**: 所有速度分量相对机体坐标系

## 📋 文件说明

### 🧪 测试文件
- `test_3d_avoidance.py`: **3D避障系统完整测试** - 测试3D网格、速度候选、安全评估
- `demo_3d_avoidance.py`: **3D避障演示** - 可视化3D避障场景和策略
- `test_run_teacher.py`: **run_teacher功能测试** - 无需AirSim的功能验证
- `test_depth_image.py`: 深度图像获取和处理测试

### 📊 辅助文件
- `FINAL_3D_SUMMARY.md`: **完整3D升级总结** - 详细的技术实现和测试结果
- `settings_rgb_deep.json`: AirSim配置文件

## 🎯 核心优势

### 🆚 与传统方法对比

| 功能 | 传统方法 | 本项目3D方法 |
|------|----------|-------------|
| 空间感知 | 水平角度区间 | **3D网格 (16x8x10)** |
| 距离计算 | 仅水平距离 | **真正3D距离** |
| 动作空间 | 1D转向角 | **3D速度向量** |
| 垂直机动 | 不支持 | **完全支持** |
| 高度管理 | 无 | **智能限制机制** |
| 避障策略 | 仅水平转向 | **3D空间机动** |

### 🚀 技术突破

- **1,280个3D网格单元** 精确建模环境
- **50个3D速度候选** 包含所有方向机动
- **2秒3D轨迹预测** 实时安全评估
- **下方40%区域可用** 智能高度限制
- **紧急垂直避障** 危险时自动上升/下降

## 📈 版本历史

### v3.0 - 真正3D避障系统 (当前版本)
- ✅ **3D网格空间划分**: 替代水平角度区间，实现真正3D感知
- ✅ **3D距离计算**: 使用真实3D欧几里得距离
- ✅ **垂直自由度**: 支持上升、下降、悬停等3D机动
- ✅ **智能高度限制**: 下方40%区域可用策略
- ✅ **实时3D轨迹预测**: 2秒预测窗口
- ✅ **完整测试验证**: 4个测试模块全部通过
- ✅ **AirSim集成**: 可在真实环境中体验3D避障

### v2.0 - 3D速度控制版本 (已废弃)
- 基于水平角度区间的3D速度输出
- 仍然局限于水平面避障

### v1.0 - 2D转向角版本 (已废弃)
- 一维转向角输出
- 仅支持水平转向

## 🎉 使用建议

1. **新用户**: 先运行 `test_3d_avoidance.py` 了解系统能力
2. **演示展示**: 运行 `demo_3d_avoidance.py` 查看可视化效果
3. **AirSim体验**: 运行 `run_teacher.py` 在真实环境中测试
4. **开发调试**: 运行 `test_run_teacher.py` 进行功能验证
5. **深入了解**: 阅读 `FINAL_3D_SUMMARY.md` 获取完整技术细节